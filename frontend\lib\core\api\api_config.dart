class ApiConfig {
  // Base URL for the backend API
  static String get baseUrl {
    // Environment-based API URL configuration
    // Development (localhost): http://127.0.0.1:5000/api/v1
    // Development (network): http://************:5000/api/v1
    // Production (Render): https://wiggyz-backend.onrender.com/api/v1
    // Previous Vercel URLs (authentication protected):
    // - https://backend-otcd5yhl0-tausifraja977-gmailcoms-projects.vercel.app/api/v1
    // - https://wiggyz-public-75pt8bev8-tausifraja977-gmailcoms-projects.vercel.app/api/v1

    // TODO: Replace with your actual Render deployment URL after deployment
    // For now, using local network for development
    // return 'http://************:5000/api/v1';

    // After Render deployment, replace above line with:
    return 'https://your-app-name.onrender.com/api/v1';
  }

  // Environment detection
  static bool get isDevelopment =>
      baseUrl.contains('127.0.0.1') || baseUrl.contains('localhost');
  static bool get isProduction => baseUrl.contains('vercel.app');

  // Connection timeout settings (in milliseconds)
  static int get connectionTimeoutMs =>
      isDevelopment ? 30000 : 15000; // 30s dev, 15s prod
  static int get receiveTimeoutMs => isDevelopment ? 30000 : 15000;

  // API Endpoints
  static const String apiPrefix = ''; // Now handled in baseUrl

  // Authentication endpoints
  static String get loginEndpoint => '/auth/login';
  static String get registerEndpoint => '/auth/register';
  static String get refreshEndpoint => '/auth/refresh';
  static String get verifyTokenEndpoint => '/auth/verify-token';
  static String get logoutEndpoint => '/auth/logout';
  static String get userProfileEndpoint => '/auth/me';

  // Wallet endpoints
  static String get walletBaseEndpoint => '/wallet';
  static String get getWalletDetailsEndpoint => walletBaseEndpoint; // GET
  static String get getWalletTransactionsEndpoint =>
      '$walletBaseEndpoint/transactions'; // GET
  static String get topUpWalletEndpoint => '$walletBaseEndpoint/topup'; // POST
  static String get withdrawWalletEndpoint =>
      '$walletBaseEndpoint/withdraw'; // POST
  static String get verifyPaymentEndpoint =>
      '$walletBaseEndpoint/verify-payment'; // POST

  // Match endpoints
  static String get matchBaseEndpoint => '/matches';
  static String get getAllMatchesEndpoint => matchBaseEndpoint; // GET
  static String get createMatchEndpoint => matchBaseEndpoint; // POST
  static String getMatchByIdEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId'; // GET
  static String getJoinMatchEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/join'; // POST
  static String getSubmitResultEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/submit-result'; // POST
  static String getUploadScreenshotEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/upload-screenshot'; // POST
  static String getUpdateMatchEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId'; // PUT
  static String getDeleteMatchEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId'; // DELETE
  static String getAssignWinnerEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/assign-winner'; // PATCH
  static String get getUserMatchesEndpoint =>
      '$matchBaseEndpoint/user/my-matches'; // GET
  static String getVerificationStatusEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/verification-status'; // GET
  static String get adminPendingResultsEndpoint =>
      '/matches/admin/pending-results'; // GET
  static String getAdminVerifyResultEndpoint(String resultId) =>
      '/matches/admin/results/$resultId/verify'; // PUT
  static String getDistributeRewardsEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/distribute-rewards'; // POST
  static String getMatchResultsEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/results'; // GET
  static String getParticipationStatusEndpoint(String matchId) =>
      '$matchBaseEndpoint/$matchId/participants/me'; // GET

  // Tournament endpoints
  static String get tournamentBaseEndpoint => '/tournaments';
  static String get getAllTournamentsEndpoint => tournamentBaseEndpoint; // GET
  static String get createTournamentEndpoint => tournamentBaseEndpoint; // POST
  static String getTournamentByIdEndpoint(String tournamentId) =>
      '$tournamentBaseEndpoint/$tournamentId'; // GET
  static String getTournamentLeaderboardEndpoint(String tournamentId) =>
      '$tournamentBaseEndpoint/$tournamentId/leaderboard'; // GET
  static String getTournamentMatchesEndpoint(String tournamentId) =>
      '$tournamentBaseEndpoint/$tournamentId/matches'; // GET
  static String get registerForTournamentEndpoint =>
      '$tournamentBaseEndpoint/register'; // POST

  // Referral endpoints
  static String get referralBaseEndpoint => '/rewards';
  static String get getUserReferralInfoEndpoint =>
      '$referralBaseEndpoint/referral-info'; // GET
  static String get getReferredUsersEndpoint =>
      '$referralBaseEndpoint/referred-users'; // GET
  static String get processReferralEndpoint =>
      '$referralBaseEndpoint/process-referral'; // POST
  static String get claimReferralRewardEndpoint =>
      '$referralBaseEndpoint/claim-referral'; // POST

  // Network timeout settings (Duration objects for compatibility)
  static Duration get connectionTimeout =>
      Duration(milliseconds: connectionTimeoutMs);
  static Duration get receiveTimeout =>
      Duration(milliseconds: receiveTimeoutMs);
}
